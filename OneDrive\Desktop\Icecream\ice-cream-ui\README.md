# Ice Cream Co. - React Website

A pixel-perfect, responsive React.js website for an ice cream company built with functional components and Tailwind CSS.

## 🍦 Features

- **Responsive Design**: Optimized for mobile and desktop devices
- **Modern React**: Built with functional components and hooks
- **Tailwind CSS**: Utility-first CSS framework for styling
- **Component-Based**: Modular, reusable components
- **Smooth Animations**: Hover effects and transitions
- **Accessibility**: Semantic HTML and proper ARIA labels

## 📁 Project Structure

```
src/
├── components/
│   ├── Navbar.jsx          # Navigation bar with mobile menu
│   ├── Hero.jsx            # Main hero section with CTA
│   ├── FeaturedProducts.jsx # Ice cream product cards
│   ├── About.jsx           # About section with features
│   ├── Contact.jsx         # Contact form and information
│   └── Footer.jsx          # Footer with links and social media
├── App.js                  # Main app component
├── App.css                 # Custom styles and scrollbar
├── index.css               # Tailwind imports and global styles
└── index.js                # React DOM entry point
```

## 🎨 Design System

### Colors
- **Primary**: Orange tones (#f3770a, #e45c00, etc.)
- **Secondary**: Blue tones (#0ea5e9, #0284c7, etc.)
- **Neutral**: Gray scale for text and backgrounds

### Typography
- **Display Font**: Poppins (headings)
- **Body Font**: Inter (body text)

### Components

#### Navbar
- Fixed navigation with logo and menu items
- Mobile-responsive hamburger menu
- Smooth scroll navigation
- CTA button

#### Hero
- Large hero section with gradient background
- Compelling headline and description
- Dual CTA buttons
- Decorative ice cream visual
- Animated background elements

#### Featured Products
- Grid layout of ice cream flavors
- Product cards with hover effects
- Pricing and descriptions
- Add to cart functionality (UI only)

#### About
- Company story and values
- Feature highlights with icons
- Statistics section
- Call-to-action button

#### Contact
- Contact form with validation
- Store information and hours
- Contact details with icons
- Responsive two-column layout

#### Footer
- Company information
- Quick links navigation
- Social media icons
- Contact information
- Copyright and legal links

## 🚀 Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Navigate to the project directory:
   ```bash
   cd ice-cream-ui
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm start
   ```

4. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

### Available Scripts

- `npm start` - Runs the app in development mode
- `npm run build` - Builds the app for production
- `npm test` - Launches the test runner
- `npm run eject` - Ejects from Create React App (one-way operation)

## 📱 Responsive Breakpoints

- **Mobile**: < 640px
- **Tablet**: 640px - 1024px
- **Desktop**: > 1024px

## 🎯 Key Features Implemented

- [x] Responsive navigation with mobile menu
- [x] Hero section with compelling copy and visuals
- [x] Product showcase with interactive cards
- [x] About section with company story
- [x] Contact form with validation
- [x] Footer with comprehensive links
- [x] Smooth scrolling navigation
- [x] Hover effects and animations
- [x] Mobile-first responsive design
- [x] Semantic HTML structure
- [x] Custom color scheme
- [x] Typography system

## 🛠 Technologies Used

- **React 19.1.0** - JavaScript library for building user interfaces
- **Tailwind CSS** - Utility-first CSS framework
- **Google Fonts** - Inter and Poppins font families
- **Create React App** - React application boilerplate

## 📄 License

This project is created for demonstration purposes.

## 🤝 Contributing

This is a demonstration project. For improvements or suggestions, please create an issue or pull request.
